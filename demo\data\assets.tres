[gd_resource type="Terrain3DAssets" load_steps=11 format=3 uid="uid://dal3jhw6241qg"]

[ext_resource type="PackedScene" uid="uid://bn5nf4esciwex" path="res://demo/assets/models/LODExample.tscn" id="1_4jrdu"]
[ext_resource type="Texture2D" uid="uid://c88j3oj0lf6om" path="res://demo/assets/textures/rock023_alb_ht.png" id="2_pog6b"]
[ext_resource type="Texture2D" uid="uid://ddprscrpsofah" path="res://demo/assets/textures/ground037_alb_ht.png" id="3_g8f2m"]
[ext_resource type="Texture2D" uid="uid://c307hdmos4gtm" path="res://demo/assets/textures/rock023_nrm_rgh.png" id="3_wncaf"]
[ext_resource type="Texture2D" uid="uid://c1ots7w6i0i1q" path="res://demo/assets/textures/ground037_nrm_rgh.png" id="4_aw5y1"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_b2vqk"]
transparency = 4
cull_mode = 2
vertex_color_use_as_albedo = true
backlight_enabled = true
backlight = Color(0.5, 0.5, 0.5, 1)
distance_fade_mode = 1
distance_fade_min_distance = 128.0
distance_fade_max_distance = 96.0

[sub_resource type="Terrain3DMeshAsset" id="Terrain3DMeshAsset_2qf8x"]
name = "TextureCard"
generated_type = 1
height_offset = 0.5
material_override = SubResource("StandardMaterial3D_b2vqk")
last_lod = 0
last_shadow_lod = 0
lod0_range = 128.0

[sub_resource type="Terrain3DMeshAsset" id="Terrain3DMeshAsset_or12t"]
name = "LODExample"
id = 1
scene_file = ExtResource("1_4jrdu")
height_offset = 0.5
last_lod = 3
last_shadow_lod = 3

[sub_resource type="Terrain3DTextureAsset" id="Terrain3DTextureAsset_lha57"]
name = "Cliff"
albedo_texture = ExtResource("2_pog6b")
normal_texture = ExtResource("3_wncaf")
normal_depth = 1.0
ao_strength = 2.0
roughness = -0.05

[sub_resource type="Terrain3DTextureAsset" id="Terrain3DTextureAsset_od0q7"]
name = "Grass"
id = 1
albedo_color = Color(0.67451, 0.74902, 0.686275, 1)
albedo_texture = ExtResource("3_g8f2m")
normal_texture = ExtResource("4_aw5y1")
normal_depth = 1.0
ao_strength = 2.0
uv_scale = 0.2
detiling_rotation = 0.161

[resource]
mesh_list = Array[Terrain3DMeshAsset]([SubResource("Terrain3DMeshAsset_2qf8x"), SubResource("Terrain3DMeshAsset_or12t")])
texture_list = Array[Terrain3DTextureAsset]([SubResource("Terrain3DTextureAsset_lha57"), SubResource("Terrain3DTextureAsset_od0q7")])
