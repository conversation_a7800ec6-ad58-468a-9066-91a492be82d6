[gd_scene load_steps=7 format=3 uid="uid://domhm87hbhbg1"]

[ext_resource type="Script" uid="uid://dajlr3n5wjwmb" path="res://demo/src/Player.gd" id="1_nm1yx"]
[ext_resource type="Script" uid="uid://b62ppvc03a6b1" path="res://demo/src/CameraManager.gd" id="2_loos7"]

[sub_resource type="SphereShape3D" id="SphereShape3D_smq6u"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_lwhhq"]
height = 1.5

[sub_resource type="SeparationRayShape3D" id="SeparationRayShape3D_twc2s"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_lsqiy"]

[node name="Player" type="CharacterBody3D"]
collision_layer = 2
script = ExtResource("1_nm1yx")

[node name="CameraManager" type="Node3D" parent="."]
script = ExtResource("2_loos7")

[node name="Arm" type="SpringArm3D" parent="CameraManager"]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.906308, 0.422618, 0, -0.422618, 0.906308, 0, 2.32515, -0.0321627)
shape = SubResource("SphereShape3D_smq6u")
spring_length = 6.0
margin = 0.5

[node name="Camera3D" type="Camera3D" parent="CameraManager/Arm"]
unique_name_in_owner = true
near = 0.25
far = 16384.0

[node name="CollisionShapeBody" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.25, 0)
shape = SubResource("CapsuleShape3D_lwhhq")

[node name="CollisionShapeRay" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 1, 0)
shape = SubResource("SeparationRayShape3D_twc2s")

[node name="Body" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
mesh = SubResource("CapsuleMesh_lsqiy")
