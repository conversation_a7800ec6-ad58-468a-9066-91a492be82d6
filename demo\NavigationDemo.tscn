[gd_scene load_steps=14 format=3 uid="uid://x34e4v60vdmy"]

[ext_resource type="Script" path="res://demo/src/DemoScene.gd" id="1_po4vw"]
[ext_resource type="PackedScene" uid="uid://bb2lp50sjndus" path="res://demo/components/Environment.tscn" id="2_i74wg"]
[ext_resource type="PackedScene" uid="uid://dwnhqfjq7v1pq" path="res://demo/components/Borders.tscn" id="3_3upu0"]
[ext_resource type="PackedScene" uid="uid://domhm87hbhbg1" path="res://demo/components/Player.tscn" id="4_fk3ul"]
[ext_resource type="PackedScene" uid="uid://djhl3foqkj4e2" path="res://demo/components/Tunnel.tscn" id="5_2lvlr"]
[ext_resource type="PackedScene" uid="uid://d2jihfohphuue" path="res://demo/components/UI.tscn" id="5_rc7e2"]
[ext_resource type="NavigationMesh" uid="uid://yf3fu23666k8" path="res://demo/data/nav_mesh.res" id="8_6jfdr"]
[ext_resource type="Terrain3DAssets" uid="uid://dal3jhw6241qg" path="res://demo/data/assets.tres" id="10_qmhh8"]
[ext_resource type="PackedScene" uid="uid://di5fovhcyd7re" path="res://demo/components/Enemy.tscn" id="12_b2xl8"]

[sub_resource type="Gradient" id="Gradient_vr1m7"]
offsets = PackedFloat32Array(0.2, 1)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_d8lcj"]
noise_type = 2
frequency = 0.03
cellular_jitter = 3.0
cellular_return_type = 0
domain_warp_enabled = true
domain_warp_type = 1
domain_warp_amplitude = 50.0
domain_warp_fractal_type = 2
domain_warp_fractal_lacunarity = 1.5
domain_warp_fractal_gain = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_bov7h"]
seamless = true
color_ramp = SubResource("Gradient_vr1m7")
noise = SubResource("FastNoiseLite_d8lcj")

[sub_resource type="Terrain3DMaterial" id="Terrain3DMaterial_s5mq5"]
_shader_parameters = {
"_mouse_layer": 2147483648,
"auto_base_texture": 0,
"auto_height_reduction": 0.1,
"auto_overlay_texture": 1,
"auto_slope": 1.0,
"bias_distance": 512.0,
"blend_sharpness": 0.87,
"depth_blur": 0.0,
"dual_scale_far": 170.0,
"dual_scale_near": 100.0,
"dual_scale_reduction": 0.3,
"dual_scale_texture": 0,
"enable_macro_variation": true,
"enable_projection": true,
"height_blending": true,
"macro_variation1": Color(0.878431, 0.862745, 0.901961, 1),
"macro_variation2": Color(0.898039, 0.898039, 0.803922, 1),
"macro_variation_slope": 0.333,
"mipmap_bias": 1.0,
"noise1_angle": 0.1,
"noise1_offset": Vector2(0.5, 0.5),
"noise1_scale": 0.04,
"noise2_scale": 0.076,
"noise3_scale": 0.225,
"noise_texture": SubResource("NoiseTexture2D_bov7h"),
"projection_angular_division": 1.436,
"projection_threshold": 0.8,
"tri_scale_reduction": 0.3
}
world_background = 0
auto_shader = true
dual_scaling = true

[node name="Demo" type="Node"]
script = ExtResource("1_po4vw")

[node name="UI" parent="." instance=ExtResource("5_rc7e2")]

[node name="Environment" parent="." instance=ExtResource("2_i74wg")]

[node name="Borders" parent="." instance=ExtResource("3_3upu0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 512, -2.5, 512)

[node name="Tunnel" parent="." instance=ExtResource("5_2lvlr")]

[node name="Player" parent="." instance=ExtResource("4_fk3ul")]
transform = Transform3D(0.0774673, 0, -0.996995, 0, 1, 0, 0.996995, 0, 0.0774673, 229.418, 104.956, -1838.16)

[node name="Enemy" parent="." node_paths=PackedStringArray("target") instance=ExtResource("12_b2xl8")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 192.727, 105.171, -1787.81)
target = NodePath("../Player")

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = ExtResource("8_6jfdr")

[node name="Terrain3D" type="Terrain3D" parent="NavigationRegion3D"]
data_directory = "res://demo/data"
material = SubResource("Terrain3DMaterial_s5mq5")
assets = ExtResource("10_qmhh8")
collision_mask = 3
mesh_size = 64
top_level = true
metadata/_edit_lock_ = true
