[gd_scene load_steps=3 format=3 uid="uid://emoubimiygn3"]

[ext_resource type="PackedScene" uid="uid://dhlr6rcvqrghb" path="res://assetPersonaggi/Cleric/Cleric.gltf" id="1_k47ug"]
[ext_resource type="Script" uid="uid://cu5i0881bbn5m" path="res://script/death.gd" id="2_p8ha7"]

[node name="ClericDeath3D" instance=ExtResource("1_k47ug")]
script = ExtResource("2_p8ha7")

[node name="CharacterArmature" parent="." index="0"]
transform = Transform3D(-0.0351436, 0, 0.999382, 0, 1, 0, -0.999382, 0, -0.0351436, 0, 0, 0)
script = ExtResource("2_p8ha7")

[node name="Skeleton3D" parent="CharacterArmature" index="0"]
bones/1/rotation = Quaternion(5.88988e-06, 0.702952, 0.711237, 5.91896e-06)
bones/2/rotation = Quaternion(-3.55269e-15, 1.19209e-07, -8.47034e-22, 1)
bones/4/rotation = Quaternion(0.231188, 3.10151e-07, 1.70243e-07, 0.972909)
bones/5/rotation = Quaternion(-0.0626712, -8.30697e-07, -1.40007e-08, 0.998034)
bones/6/rotation = Quaternion(-0.0362502, 6.77571e-07, -2.51756e-09, 0.999343)
bones/7/rotation = Quaternion(4.65662e-09, -0.000275475, -8.05199e-13, 1)
bones/9/position = Vector3(0.126103, 0.141414, 0.0126461)
bones/10/rotation = Quaternion(0.573699, 0.449411, 0.513707, -0.452773)
bones/11/rotation = Quaternion(-0.25967, 0.217113, -0.193421, 0.920881)
bones/12/rotation = Quaternion(0.0198003, 0.0174427, -0.00852202, 0.999615)
bones/13/rotation = Quaternion(-0.0626413, -0.0188104, -0.00118084, 0.997858)
bones/14/rotation = Quaternion(-0.14578, 2.48149e-08, 3.2529e-08, 0.989317)
bones/15/rotation = Quaternion(-0.171058, 0.0837446, -0.432979, 0.881054)
bones/16/rotation = Quaternion(-0.0789317, -0.297914, 0.141663, 0.940717)
bones/17/position = Vector3(-0.124826, 0.141414, 0.0126462)
bones/17/rotation = Quaternion(-0.0330208, 0.0149541, 0.412281, 0.910335)
bones/18/rotation = Quaternion(0.573699, -0.449412, -0.513707, -0.452773)
bones/19/rotation = Quaternion(-0.25967, -0.217113, 0.193421, 0.920881)
bones/20/rotation = Quaternion(0.0198003, -0.0174426, 0.00852203, 0.999615)
bones/21/rotation = Quaternion(-0.0626414, 0.0188104, 0.00118083, 0.997858)
bones/22/rotation = Quaternion(-0.14578, -2.6077e-08, -2.98023e-08, 0.989317)
bones/23/position = Vector3(0.0209792, -0.0180254, -7.81579e-08)
bones/23/rotation = Quaternion(9.49949e-08, -7.1479e-08, 3.72528e-09, 1)
bones/25/rotation = Quaternion(-0.171058, -0.0837446, 0.432979, 0.881054)
bones/26/rotation = Quaternion(-0.0789317, 0.297914, -0.141663, 0.940717)
bones/28/rotation = Quaternion(0.995473, 0.00855835, -0.000940769, 0.0946593)
bones/29/rotation = Quaternion(0.201978, 0.0020952, 0.00781443, 0.979357)
bones/30/rotation = Quaternion(0.995473, -0.00855844, 0.000941474, 0.0946589)
bones/31/rotation = Quaternion(0.201978, -0.00209518, -0.00781443, 0.979357)
bones/33/rotation = Quaternion(-6.1438e-06, 0.702952, 0.711237, -5.58376e-06)

[node name="Head_2" parent="CharacterArmature/Skeleton3D" index="0"]
transform = Transform3D(1, -6.93747e-13, 4.65661e-10, 2.54659e-11, 0.999224, -0.0393955, -5.23869e-10, 0.0393955, 0.999224, 0.00794286, 2.68079, -0.157512)

[node name="Cleric_Staff" parent="CharacterArmature/Skeleton3D" index="1"]
transform = Transform3D(-0.0476717, -0.90516, 0.422388, 0.974522, 0.0506365, 0.218499, -0.219165, 0.422043, 0.879685, -0.426875, 0.914092, 0.129128)

[node name="ShoulderPads" parent="CharacterArmature/Skeleton3D" index="2"]
transform = Transform3D(1, 2.11742e-12, -1.42109e-12, -2.25953e-12, 0.999224, -0.0393955, 1.59162e-12, 0.0393955, 0.999224, -5.82077e-11, -0.0270432, 0.122815)

[node name="Camera3D" type="Camera3D" parent="." index="2"]
transform = Transform3D(-0.126257, 0.00187709, 0.991996, -0.0124871, 0.999916, -0.00348138, -0.991919, -0.0128267, -0.126223, 5, 1, 0)
fov = 47.8125
