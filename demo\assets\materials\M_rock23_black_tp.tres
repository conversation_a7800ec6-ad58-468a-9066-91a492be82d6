[gd_resource type="StandardMaterial3D" load_steps=3 format=3 uid="uid://d0hyi5n6ng25w"]

[ext_resource type="Texture2D" uid="uid://c88j3oj0lf6om" path="res://demo/assets/textures/rock023_alb_ht.png" id="1_1js8i"]
[ext_resource type="Texture2D" uid="uid://c307hdmos4gtm" path="res://demo/assets/textures/rock023_nrm_rgh.png" id="2_snl3x"]

[resource]
albedo_color = Color(0.501406, 0.501407, 0.501406, 1)
albedo_texture = ExtResource("1_1js8i")
roughness = 0.95
roughness_texture = ExtResource("2_snl3x")
roughness_texture_channel = 3
normal_enabled = true
normal_texture = ExtResource("2_snl3x")
uv1_scale = Vector3(0.4, 0.4, 0.4)
uv1_triplanar = true
uv1_world_triplanar = true
texture_filter = 5
