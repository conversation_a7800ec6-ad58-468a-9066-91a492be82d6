[gd_scene load_steps=9 format=3 uid="uid://blaieaqp413k7"]

[ext_resource type="Script" path="res://addons/terrain_3d/tools/importer.gd" id="1_60b8f"]

[sub_resource type="Gradient" id="Gradient_88f3t"]
offsets = PackedFloat32Array(0.2, 1)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_muvel"]
noise_type = 2
frequency = 0.03
cellular_jitter = 3.0
cellular_return_type = 0
domain_warp_enabled = true
domain_warp_type = 1
domain_warp_amplitude = 50.0
domain_warp_fractal_type = 2
domain_warp_fractal_lacunarity = 1.5
domain_warp_fractal_gain = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ve0yk"]
seamless = true
color_ramp = SubResource("Gradient_88f3t")
noise = SubResource("FastNoiseLite_muvel")

[sub_resource type="Terrain3DMaterial" id="Terrain3DMaterial_p55u0"]
_shader_parameters = {
"blend_sharpness": 0.87,
"height_blending": true,
"macro_variation1": Color(1, 1, 1, 1),
"macro_variation2": Color(1, 1, 1, 1),
"noise1_angle": 0.0,
"noise1_offset": Vector2(0.5, 0.5),
"noise1_scale": 0.04,
"noise2_scale": 0.076,
"noise3_scale": 0.225,
"noise_texture": SubResource("NoiseTexture2D_ve0yk"),
"vertex_normals_distance": 128.0
}
show_checkered = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8rvqy"]
cull_mode = 2
vertex_color_use_as_albedo = true
backlight_enabled = true
backlight = Color(0.5, 0.5, 0.5, 1)

[sub_resource type="Terrain3DMeshAsset" id="Terrain3DMeshAsset_7je72"]
height_offset = 0.5
density = 10.0
material_override = SubResource("StandardMaterial3D_8rvqy")
generated_type = 1

[sub_resource type="Terrain3DAssets" id="Terrain3DAssets_op32e"]
mesh_list = Array[Terrain3DMeshAsset]([SubResource("Terrain3DMeshAsset_7je72")])

[node name="Importer" type="Terrain3D"]
material = SubResource("Terrain3DMaterial_p55u0")
assets = SubResource("Terrain3DAssets_op32e")
mesh_lods = 8
top_level = true
script = ExtResource("1_60b8f")
metadata/_edit_lock_ = true
