[gd_scene load_steps=8 format=3 uid="uid://cofnhdcclon1w"]

[ext_resource type="Script" path="res://demo/src/CodeGenerated.gd" id="1_h7vyv"]
[ext_resource type="PackedScene" uid="uid://domhm87hbhbg1" path="res://demo/components/Player.tscn" id="2_3v2uf"]
[ext_resource type="PackedScene" uid="uid://bb2lp50sjndus" path="res://demo/components/Environment.tscn" id="3_71ikj"]
[ext_resource type="PackedScene" uid="uid://di5fovhcyd7re" path="res://demo/components/Enemy.tscn" id="4_p8qry"]
[ext_resource type="PackedScene" uid="uid://d2jihfohphuue" path="res://demo/components/UI.tscn" id="4_x5ge4"]
[ext_resource type="Script" path="res://demo/src/RuntimeNavigationBaker.gd" id="5_445ur"]

[sub_resource type="NavigationMesh" id="NavigationMesh_vs6am"]
geometry_parsed_geometry_type = 1
agent_height = 2.0
agent_max_slope = 30.0

[node name="CodeGenerated" type="Node"]
script = ExtResource("1_h7vyv")

[node name="Environment" parent="." instance=ExtResource("3_71ikj")]

[node name="Player" parent="." instance=ExtResource("2_3v2uf")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 50, 0)

[node name="Enemy" parent="." node_paths=PackedStringArray("target") instance=ExtResource("4_p8qry")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 50, -10)
target = NodePath("../Player")

[node name="UI" parent="." instance=ExtResource("4_x5ge4")]

[node name="RuntimeNavigationBaker" type="Node" parent="." node_paths=PackedStringArray("player")]
script = ExtResource("5_445ur")
enabled = false
template = SubResource("NavigationMesh_vs6am")
player = NodePath("../Player")

[node name="RunThisSceneLabel3D" type="Label3D" parent="."]
pixel_size = 0.001
billboard = 1
no_depth_test = true
fixed_size = true
text = "Run This Scene"
font_size = 64
outline_size = 10
