[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://c8cx4xjwluvxw"
path="res://.godot/imported/RockC.glb-5881ee1ff2072066a3de122ce4239bee.scn"

[deps]

source_file="res://demo/assets/models/RockC.glb"
dest_files=["res://.godot/imported/RockC.glb-5881ee1ff2072066a3de122ce4239bee.scn"]

[params]

nodes/root_type="StaticBody3D"
nodes/root_name="Scene Root"
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={
"materials": {
"@MATERIAL:0": {
"use_external/enabled": true,
"use_external/path": "res://demo/assets/materials/M_rock30.tres"
}
}
}
gltf/naming_version=0
gltf/embedded_image_handling=1
