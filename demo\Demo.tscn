[gd_scene load_steps=18 format=3 uid="uid://chol2xlfbq7cu"]

[ext_resource type="Script" uid="uid://chstoagn42gbr" path="res://demo/src/DemoScene.gd" id="1_k7qca"]
[ext_resource type="PackedScene" uid="uid://d2jihfohphuue" path="res://demo/components/UI.tscn" id="2_nqak5"]
[ext_resource type="PackedScene" uid="uid://dwnhqfjq7v1pq" path="res://demo/components/Borders.tscn" id="3_cw38j"]
[ext_resource type="PackedScene" uid="uid://djhl3foqkj4e2" path="res://demo/components/Tunnel.tscn" id="3_kdh0b"]
[ext_resource type="PackedScene" uid="uid://bb2lp50sjndus" path="res://demo/components/Environment.tscn" id="3_yqldq"]
[ext_resource type="Script" uid="uid://dajlr3n5wjwmb" path="res://demo/src/Player.gd" id="6_fhs52"]
[ext_resource type="Script" uid="uid://b62ppvc03a6b1" path="res://demo/src/CameraManager.gd" id="7_jkl3n"]
[ext_resource type="PackedScene" uid="uid://dhlr6rcvqrghb" path="res://assetPersonaggi/Cleric/Cleric.gltf" id="8_877j2"]
[ext_resource type="PackedScene" uid="uid://d3sr0a7dxfkr8" path="res://addons/terrain_3d/extras/particle_example/Terrain3DParticles.tscn" id="8_fwrtk"]
[ext_resource type="Terrain3DAssets" uid="uid://dal3jhw6241qg" path="res://demo/data/assets.tres" id="8_g2of2"]

[sub_resource type="SphereShape3D" id="SphereShape3D_smq6u"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_lwhhq"]
height = 1.5

[sub_resource type="SeparationRayShape3D" id="SeparationRayShape3D_twc2s"]

[sub_resource type="Gradient" id="Gradient_vr1m7"]
offsets = PackedFloat32Array(0.2, 1)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_d8lcj"]
noise_type = 2
frequency = 0.03
cellular_jitter = 3.0
cellular_return_type = 0
domain_warp_enabled = true
domain_warp_type = 1
domain_warp_amplitude = 50.0
domain_warp_fractal_type = 2
domain_warp_fractal_lacunarity = 1.5
domain_warp_fractal_gain = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_bov7h"]
seamless = true
color_ramp = SubResource("Gradient_vr1m7")
noise = SubResource("FastNoiseLite_d8lcj")

[sub_resource type="Terrain3DMaterial" id="Terrain3DMaterial_jrc01"]
_shader_parameters = {
"_mouse_layer": 2147483648,
"auto_base_texture": 0,
"auto_height_reduction": 0.05,
"auto_overlay_texture": 1,
"auto_slope": 0.45,
"bias_distance": 512.0,
"blend_sharpness": 0.5,
"depth_blur": 0.0,
"dual_scale_far": 170.0,
"dual_scale_near": 100.0,
"dual_scale_reduction": 0.3,
"dual_scale_texture": 0,
"enable_macro_variation": true,
"enable_projection": true,
&"flat_terrain_normals": false,
"macro_variation1": Color(0.855, 0.8625, 0.9, 1),
"macro_variation2": Color(0.9, 0.885, 0.81, 1),
"macro_variation_slope": 0.333,
"mipmap_bias": 1.0,
"noise1_angle": 0.1,
"noise1_offset": Vector2(0.5, 0.5),
"noise1_scale": 0.04,
"noise2_scale": 0.076,
"noise_texture": SubResource("NoiseTexture2D_bov7h"),
"projection_threshold": 0.87,
"tri_scale_reduction": 0.3,
"world_noise_fragment_normals": false,
"world_noise_height": 34.0,
"world_noise_lod_distance": 7500.0,
"world_noise_max_octaves": 4,
"world_noise_min_octaves": 2,
"world_noise_offset": Vector3(2.17, -1.225, 1.9),
"world_noise_region_blend": 0.33,
"world_noise_scale": 9.85
}
world_background = 2
auto_shader = true
dual_scaling = true

[node name="Demo" type="Node"]
script = ExtResource("1_k7qca")

[node name="UI" parent="." instance=ExtResource("2_nqak5")]

[node name="Environment" parent="." instance=ExtResource("3_yqldq")]

[node name="Borders" parent="." instance=ExtResource("3_cw38j")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 512, -3, 512)
collision_mask = 3

[node name="Tunnel" parent="." instance=ExtResource("3_kdh0b")]

[node name="Player" type="CharacterBody3D" parent="."]
transform = Transform3D(0.791779, -0.320488, 0.519973, 0, 0.851289, 0.524697, -0.610807, -0.415444, 0.674033, 1278.26, 352.966, 1072.77)
collision_layer = 2
script = ExtResource("6_fhs52")

[node name="CameraManager" type="Node3D" parent="Player"]
script = ExtResource("7_jkl3n")

[node name="Arm" type="SpringArm3D" parent="Player/CameraManager"]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.906308, 0.422618, 0, -0.422618, 0.906308, 0, 2.32515, -0.0321627)
shape = SubResource("SphereShape3D_smq6u")
spring_length = 6.0
margin = 0.5

[node name="Camera3D" type="Camera3D" parent="Player/CameraManager/Arm"]
unique_name_in_owner = true
near = 0.25
far = 16384.0

[node name="CollisionShapeBody" type="CollisionShape3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.25, 0)
shape = SubResource("CapsuleShape3D_lwhhq")

[node name="CollisionShapeRay" type="CollisionShape3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 1, 0)
shape = SubResource("SeparationRayShape3D_twc2s")

[node name="Cleric2" parent="Player" instance=ExtResource("8_877j2")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, -2.98023e-08, -2.98023e-08, 0, 1, 6.10352e-05, 1, 0)

[node name="Terrain3D" type="Terrain3D" parent="."]
data_directory = "res://demo/data"
material = SubResource("Terrain3DMaterial_jrc01")
assets = ExtResource("8_g2of2")
collision_mask = 3
top_level = true
metadata/_edit_lock_ = true

[node name="Terrain3DParticles" parent="Terrain3D" node_paths=PackedStringArray("terrain") instance=ExtResource("8_fwrtk")]
terrain = NodePath("..")
