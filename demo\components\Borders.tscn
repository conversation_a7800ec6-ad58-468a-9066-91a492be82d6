[gd_scene load_steps=6 format=3 uid="uid://dwnhqfjq7v1pq"]

[ext_resource type="Material" uid="uid://d0hyi5n6ng25w" path="res://demo/assets/materials/M_rock23_black_tp.tres" id="1_goand"]

[sub_resource type="BoxMesh" id="BoxMesh_1kfaq"]
size = Vector3(1025.01, 4, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_s0c8u"]
size = Vector3(1025, 100, 1)

[sub_resource type="BoxMesh" id="BoxMesh_kfxc8"]
size = Vector3(3072, 4, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_pyqb2"]
size = Vector3(1, 100, 3072)

[node name="Borders" type="StaticBody3D"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 512)
mesh = SubResource("BoxMesh_1kfaq")
skeleton = NodePath("../..")
surface_material_override/0 = ExtResource("1_goand")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 50, 512)
shape = SubResource("BoxShape3D_s0c8u")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, -2560)
mesh = SubResource("BoxMesh_1kfaq")
skeleton = NodePath("../..")
surface_material_override/0 = ExtResource("1_goand")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 50, -2560)
shape = SubResource("BoxShape3D_s0c8u")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="."]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 512, 2, -1024)
mesh = SubResource("BoxMesh_kfxc8")
skeleton = NodePath("../..")
surface_material_override/0 = ExtResource("1_goand")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 512, 50, -1024)
shape = SubResource("BoxShape3D_pyqb2")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="."]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -512, 2, -1024)
mesh = SubResource("BoxMesh_kfxc8")
skeleton = NodePath("../..")
surface_material_override/0 = ExtResource("1_goand")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -512, 50, -1024)
shape = SubResource("BoxShape3D_pyqb2")
