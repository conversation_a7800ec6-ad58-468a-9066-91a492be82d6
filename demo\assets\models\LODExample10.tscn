[gd_scene load_steps=21 format=3 uid="uid://dqwgv1ahsvqio"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_788j8"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="SphereMesh" id="SphereMesh_u4ac2"]
material = SubResource("StandardMaterial3D_788j8")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_0qa1y"]
albedo_color = Color(1, 0.907, 0.38, 1)

[sub_resource type="TorusMesh" id="TorusMesh_tjrnq"]
material = SubResource("StandardMaterial3D_0qa1y")
inner_radius = 0.141
outer_radius = 0.601

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3i52q"]
albedo_color = Color(1, 0.540167, 0.11, 1)

[sub_resource type="BoxMesh" id="BoxMesh_xyuxq"]
material = SubResource("StandardMaterial3D_3i52q")
size = Vector3(0.85, 0.85, 0.85)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_p0ha4"]
albedo_color = Color(0.48, 1, 0.497333, 1)

[sub_resource type="PrismMesh" id="PrismMesh_xnm4h"]
material = SubResource("StandardMaterial3D_p0ha4")
size = Vector3(1, 1, 0.855)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jv6fb"]
albedo_color = Color(0.3384, 0.538933, 0.94, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_i1yhp"]
material = SubResource("StandardMaterial3D_jv6fb")
top_radius = 0.0
bottom_radius = 0.59
height = 1.08
radial_segments = 4

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_0qk4s"]
albedo_color = Color(0.62325, 0.495, 0.9, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_bvmel"]
material = SubResource("StandardMaterial3D_0qk4s")
top_radius = 0.765
bottom_radius = 0.34
height = 1.54

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_438mn"]
albedo_color = Color(0.933333, 0.2, 1, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_x8oip"]
material = SubResource("StandardMaterial3D_438mn")
top_radius = 0.33
bottom_radius = 0.34
height = 1.0
radial_segments = 7

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hqr78"]
albedo_color = Color(0.51, 0.456705, 0.3417, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_dp7xj"]
material = SubResource("StandardMaterial3D_hqr78")
top_radius = 0.29
bottom_radius = 0.59
height = 0.65
radial_segments = 4

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_aba40"]
albedo_color = Color(0.1512, 0.36, 0.17208, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_8ctts"]
material = SubResource("StandardMaterial3D_aba40")
top_radius = 0.45
bottom_radius = 0.495
height = 1.54
radial_segments = 4

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_tl8dq"]
albedo_color = Color(0.2279, 0.36888, 0.53, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_w2aj8"]
material = SubResource("StandardMaterial3D_tl8dq")
top_radius = 0.0
bottom_radius = 0.77
height = 1.54
radial_segments = 4

[node name="TestMultimesh" type="Node3D"]

[node name="Node3D" type="Node3D" parent="."]

[node name="MeshInstance3D0" type="MeshInstance3D" parent="Node3D"]
mesh = SubResource("SphereMesh_u4ac2")
skeleton = NodePath("../..")

[node name="MeshInstance3D1" type="MeshInstance3D" parent="Node3D"]
mesh = SubResource("TorusMesh_tjrnq")
skeleton = NodePath("../..")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="Node3D"]
mesh = SubResource("BoxMesh_xyuxq")
skeleton = NodePath("../..")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.118133, 0)
mesh = SubResource("PrismMesh_xnm4h")
skeleton = NodePath("../..")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_i1yhp")
skeleton = NodePath("../..")

[node name="MeshInstance3D5" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_bvmel")
skeleton = NodePath("../..")

[node name="MeshInstance3D6" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_x8oip")
skeleton = NodePath("../..")

[node name="MeshInstance3D7" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_dp7xj")
skeleton = NodePath("../..")

[node name="MeshInstance3D8" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_8ctts")
skeleton = NodePath("../..")

[node name="MeshInstance3D9" type="MeshInstance3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.246009, 0)
mesh = SubResource("CylinderMesh_w2aj8")
skeleton = NodePath("../..")
