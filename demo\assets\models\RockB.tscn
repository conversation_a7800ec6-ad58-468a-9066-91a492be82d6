[gd_scene load_steps=4 format=3 uid="uid://bwvtgwartxt0g"]

[ext_resource type="PackedScene" uid="uid://nta3sef6c2el" path="res://demo/assets/models/RockB.glb" id="1_2nhli"]
[ext_resource type="Material" uid="uid://nbbdrx8vma80" path="res://demo/assets/materials/M_rock23_tp.tres" id="2_dbm2k"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_wfrmp"]
data = PackedVector3Array(0.4584, -3.889, 0.8266, 0.6021, -4.1961, -0.0392, 0.169, -4.9369, 0.2441, 2.1498, -1.9824, 1.4097, 2.1074, -0.9551, 0.9434, 2.3387, -2.1373, 0.6494, -0.4293, -5.0774, 0.0003, -0.5108, -3.8408, -1.1476, -1.1208, -4.5065, 0.1953, 0.0671, -5.0216, -0.4515, 0.4756, -4.5162, -0.6549, 0.1398, -4.5397, -0.9338, 0.169, -4.9369, 0.2441, -0.0922, -4.5871, 1.3087, 0.4584, -3.889, 0.8266, 2.1498, -1.9824, 1.4097, 1.9393, -0.8033, 1.6608, 2.1074, -0.9551, 0.9434, -1.2288, -2.368, 2.4204, -1.4963, -0.9921, 2.5267, -0.7814, -0.8418, 2.7775, -2.6648, -1.8501, 0.307, -2.907, 0.041, -0.3088, -2.5202, -0.0318, 0.8043, -1.0077, -1.3716, -2.1657, -0.7692, -0.6389, -2.6654, -1.6842, -0.4729, -2.3712, 2.1195, -1.771, -1.5705, 2.5987, -0.8743, -1.1872, 1.9584, -0.8816, -2.0923, -0.2829, -3.5923, 2.1108, -0.2939, -2.4569, 2.4482, 0.5442, -2.0915, 2.2664, -1.1208, -4.5065, 0.1953, -2.179, -3.4152, 0.4259, -1.8849, -3.8097, 1.1593, -0.5108, -3.8408, -1.1476, -0.2695, -2.508, -2.0432, -1.1054, -2.8992, -1.0824, 2.1726, -2.8941, -0.5813, 1.87, -2.8735, -1.1419, 1.5863, -3.2514, -0.7541, 0.7315, 0.8274, 2.7196, 1.1136, 1.849, 2.4998, 1.409, 0.9631, 2.386, -0.4271, 2.5556, 2.4897, 0.5261, 4.2194, 1.9538, 0.5038, 3.0339, 2.4087, -1.9764, 3.1849, 0.2685, -1.0116, 3.5059, 0.8103, -1.5979, 2.7577, 1.2075, -0.9393, 2.872, -1.8539, -1.2485, 3.6723, -1.0264, -1.7216, 2.2099, -1.5001, 1.1827, 2.8613, -1.4341, 0.9077, 3.2903, -1.4616, 0.9177, 2.8679, -1.5387, 0.8594, 5.2909, 1.0912, 0.6378, 5.6752, 0.4017, 1.3249, 5.3073, 0.5102, -1.1208, -4.5065, 0.1953, -1.8849, -3.8097, 1.1593, -0.8729, -4.7982, 1.121, 2.4617, 0.7259, 0.0288, 2.5473, 1.387, -0.0726, 2.6196, 0.7185, -0.2944, 0.9445, 5.5277, -0.3666, 0.5746, 5.6649, -0.3989, 0.6884, 5.4332, -0.7011, 1.7744, 4.3299, -0.4393, 1.3226, 4.3217, -0.9655, 2.0312, 2.7158, -1.0135, 0.9877, 0.4313, -2.4389, 0.7444, 0.8429, -2.2856, 0.635, 0.4247, -2.5749, -0.1392, 4.5027, -1.2571, -0.2125, 5.045, -0.6732, -1.2485, 3.6723, -1.0264, -0.1392, 4.5027, -1.2571, -1.2485, 3.6723, -1.0264, -0.9393, 2.872, -1.8539, -1.7965, 0.899, -2.2295, -1.7216, 2.2099, -1.5001, -2.3834, 0.7782, -1.5631, -0.791, 4.3118, -0.0929, -0.2232, 5.1794, 0.1938, -0.5404, 4.4098, 0.4328, -1.3695, 3.5538, -0.0189, -1.0116, 3.5059, 0.8103, -1.9764, 3.1849, 0.2685, -2.5202, -0.0318, 0.8043, -2.1461, 1.5782, 1.2673, -2.0758, -0.0137, 1.8949, -0.5404, 4.4098, 0.4328, -0.2232, 5.1794, 0.1938, -0.0735, 4.938, 0.8308, -1.0116, 3.5059, 0.8103, -0.2982, 4.0929, 1.4648, -0.878, 3.135, 1.7811, 2.6003, 1.0473, -1.0133, 2.4031, 2.6779, -0.4122, 2.0312, 2.7158, -1.0135, 2.4031, 2.6779, -0.4122, 1.7744, 4.3299, -0.4393, 2.0312, 2.7158, -1.0135, -1.0159, 0.9756, 2.6776, -0.4271, 2.5556, 2.4897, -0.2195, 1.1266, 2.8293, -0.0823, 1.3076, -2.4751, 0.0456, 1.662, -2.2329, -0.2095, 1.8203, -2.3278, 0.1709, 2.9305, -1.8154, 0.1823, 3.4347, -1.7089, -0.0988, 3.0024, -1.9036, -2.907, 0.041, -0.3088, -2.575, 1.9843, 0.2578, -2.5202, -0.0318, 0.8043, -2.5502, 1.5321, -0.8246, -1.9974, 2.3952, -0.8364, -2.286, 2.7057, -0.3882, -1.9974, 2.3952, -0.8364, -1.7496, 3.0465, -0.6307, -2.286, 2.7057, -0.3882, 0.8594, 5.2909, 1.0912, 1.3249, 5.3073, 0.5102, 1.6148, 4.6612, 1.1746, -1.6176, 1.4128, 2.2491, -1.7058, 2.0492, 1.8707, -1.3001, 2.2705, 2.1959, -1.5979, 2.7577, 1.2075, -1.0116, 3.5059, 0.8103, -0.878, 3.135, 1.7811, -0.7261, 0.8081, -2.8158, -0.8202, 1.5078, -2.6285, -1.246, 0.719, -2.7492, 1.409, 0.9631, 2.386, 1.1136, 1.849, 2.4998, 1.6219, 2.267, 2.1861, 0.5038, 3.0339, 2.4087, 0.5261, 4.2194, 1.9538, 1.2634, 3.7734, 1.9919, 2.6003, 1.0473, -1.0133, 2.0312, 2.7158, -1.0135, 2.1515, 0.9506, -1.6595, 2.2544, 3.8319, 0.7934, 2.1174, 4.2399, 0.396, 2.3685, 3.5019, 0.414, 1.4453, 1.3219, -1.8647, 1.4442, 1.9, -1.6223, 1.2471, 1.631, -1.7857, 1.9293, 0.908, 1.6849, 2.1901, 2.3652, 1.3543, 2.1038, 1.0245, 0.9114, -1.0077, -1.3716, -2.1657, -1.6842, -0.4729, -2.3712, -1.6361, -1.5711, -1.5378, -1.7965, 0.899, -2.2295, -0.9393, 2.872, -1.8539, -1.7216, 2.2099, -1.5001, 1.1377, -0.8428, -2.7439, 0.7304, -0.9533, -2.8604, 0.9457, -1.6804, -2.7624, -2.6648, -1.8501, 0.307, -2.5202, -0.0318, 0.8043, -2.3406, -2.1709, 1.3672, -2.5202, -0.0318, 0.8043, -2.575, 1.9843, 0.2578, -2.1461, 1.5782, 1.2673, -1.6842, -0.4729, -2.3712, -2.3999, -0.6295, -1.5513, -1.6361, -1.5711, -1.5378, -0.2939, -2.4569, 2.4482, 0.256, -0.9397, 2.7009, 0.5442, -2.0915, 2.2664, -0.2195, 1.1266, 2.8293, -0.4271, 2.5556, 2.4897, 0.5038, 3.0339, 2.4087, 2.4385, -0.7713, 0.0524, 2.634, -0.7691, -0.3318, 2.5652, -1.538, -0.1083, 1.8065, -2.8661, 1.2715, 1.9869, -3.1191, 0.2426, 1.2817, -3.3103, 0.5621, 2.1038, 1.0245, 0.9114, 2.1901, 2.3652, 1.3543, 2.3678, 2.1513, 0.6399, 0.9342, -3.6502, -1.2505, 0.9178, -3.3811, -1.9588, 0.3423, -3.927, -1.4233, 0.9342, -3.6502, -1.2505, 1.4243, -3.0391, -1.5379, 0.9178, -3.3811, -1.9588, 2.1195, -1.771, -1.5705, 1.9584, -0.8816, -2.0923, 1.5426, -2.0265, -2.2438, -0.5108, -3.8408, -1.1476, -1.1054, -2.8992, -1.0824, -1.1208, -4.5065, 0.1953, 0.3194, -2.8417, -2.3407, 0.0974, -1.7758, -2.6186, -0.2695, -2.508, -2.0432, -0.5108, -3.8408, -1.1476, 0.3194, -2.8417, -2.3407, -0.2695, -2.508, -2.0432, -2.5202, -0.0318, 0.8043, -2.0758, -0.0137, 1.8949, -2.3406, -2.1709, 1.3672, -1.9059, -2.3487, -0.7483, -2.5883, -1.4508, -0.7517, -2.3688, -2.5627, -0.2622, -1.809, -3.263, -0.2744, -1.9059, -2.3487, -0.7483, -2.3688, -2.5627, -0.2622, -0.0922, -4.5871, 1.3087, 0.4197, -3.4404, 1.5487, 0.4584, -3.889, 0.8266, -1.7887, -3.0231, 1.9828, -1.4963, -0.9921, 2.5267, -1.2288, -2.368, 2.4204, 2.5358, -1.9021, -0.8254, 2.5987, -0.8743, -1.1872, 2.1195, -1.771, -1.5705, 0.256, -0.9397, 2.7009, 1.2883, -0.8696, 2.2885, 0.5442, -2.0915, 2.2664, 1.2817, -3.3103, 0.5621, 0.9704, -3.0065, 1.4497, 1.8065, -2.8661, 1.2715, -1.1122, -3.8993, 1.9946, -1.7887, -3.0231, 1.9828, -1.2288, -2.368, 2.4204, 1.9869, -3.1191, 0.2426, 1.267, -3.4909, -0.189, 1.2817, -3.3103, 0.5621, 0.5442, -2.0915, 2.2664, 1.2883, -0.8696, 2.2885, 1.6056, -1.9424, 1.9813, -0.4293, -5.0774, 0.0003, 0.169, -4.9369, 0.2441, 0.0671, -5.0216, -0.4515, 2.1498, -1.9824, 1.4097, 1.8065, -2.8661, 1.2715, 1.6056, -1.9424, 1.9813, -2.179, -3.4152, 0.4259, -2.3688, -2.5627, -0.2622, -2.6648, -1.8501, 0.307, 1.87, -2.8735, -1.1419, 2.1195, -1.771, -1.5705, 1.4243, -3.0391, -1.5379, -1.7058, 2.0492, 1.8707, -2.1461, 1.5782, 1.2673, -1.5979, 2.7577, 1.2075, 1.2471, 1.631, -1.7857, 0.0456, 1.662, -2.2329, 0.7444, 0.8429, -2.2856, 0.0456, 1.662, -2.2329, 0.9177, 2.8679, -1.5387, 0.1709, 2.9305, -1.8154, 0.0456, 1.662, -2.2329, 1.2471, 1.631, -1.7857, 0.9177, 2.8679, -1.5387, 2.5473, 1.387, -0.0726, 2.3685, 3.5019, 0.414, 2.4031, 2.6779, -0.4122, 2.5473, 1.387, -0.0726, 2.3678, 2.1513, 0.6399, 2.3685, 3.5019, 0.414, 0.5746, 5.6649, -0.3989, -0.2232, 5.1794, 0.1938, -0.2125, 5.045, -0.6732, 0.5746, 5.6649, -0.3989, 0.6378, 5.6752, 0.4017, -0.2232, 5.1794, 0.1938, -0.0922, -4.5871, 1.3087, -1.1122, -3.8993, 1.9946, -0.2829, -3.5923, 2.1108, -0.0922, -4.5871, 1.3087, -0.8729, -4.7982, 1.121, -1.1122, -3.8993, 1.9946, 0.9704, -3.0065, 1.4497, 0.4197, -3.4404, 1.5487, 0.5442, -2.0915, 2.2664, 2.3387, -2.1373, 0.6494, 2.1726, -2.8941, -0.5813, 1.9869, -3.1191, 0.2426, 2.1726, -2.8941, -0.5813, 2.5652, -1.538, -0.1083, 2.5358, -1.9021, -0.8254, 2.1726, -2.8941, -0.5813, 2.3387, -2.1373, 0.6494, 2.5652, -1.538, -0.1083, 0.9342, -3.6502, -1.2505, 1.267, -3.4909, -0.189, 1.5863, -3.2514, -0.7541, 1.267, -3.4909, -0.189, 0.4756, -4.5162, -0.6549, 0.6021, -4.1961, -0.0392, 1.267, -3.4909, -0.189, 0.9342, -3.6502, -1.2505, 0.4756, -4.5162, -0.6549, -1.8849, -3.8097, 1.1593, -2.3406, -2.1709, 1.3672, -1.7887, -3.0231, 1.9828, -0.5108, -3.8408, -1.1476, 0.1398, -4.5397, -0.9338, 0.3423, -3.927, -1.4233, -1.1054, -2.8992, -1.0824, -1.6361, -1.5711, -1.5378, -1.9059, -2.3487, -0.7483, 0.9178, -3.3811, -1.9588, 0.9457, -1.6804, -2.7624, 0.3194, -2.8417, -2.3407, 0.9178, -3.3811, -1.9588, 1.5426, -2.0265, -2.2438, 0.9457, -1.6804, -2.7624, 2.4617, 0.7259, 0.0288, 2.1074, -0.9551, 0.9434, 2.1038, 1.0245, 0.9114, 2.4617, 0.7259, 0.0288, 2.4385, -0.7713, 0.0524, 2.1074, -0.9551, 0.9434, 2.6196, 0.7185, -0.2944, 2.5987, -0.8743, -1.1872, 2.634, -0.7691, -0.3318, 2.6196, 0.7185, -0.2944, 2.6003, 1.0473, -1.0133, 2.5987, -0.8743, -1.1872, 0.256, -0.9397, 2.7009, -0.2195, 1.1266, 2.8293, 0.7315, 0.8274, 2.7196, 0.256, -0.9397, 2.7009, -0.7814, -0.8418, 2.7775, -0.2195, 1.1266, 2.8293, 1.9393, -0.8033, 1.6608, 1.409, 0.9631, 2.386, 1.9293, 0.908, 1.6849, 1.9393, -0.8033, 1.6608, 1.2883, -0.8696, 2.2885, 1.409, 0.9631, 2.386, -2.0758, -0.0137, 1.8949, -1.0159, 0.9756, 2.6776, -1.4963, -0.9921, 2.5267, -2.0758, -0.0137, 1.8949, -1.6176, 1.4128, 2.2491, -1.0159, 0.9756, 2.6776, -1.246, 0.719, -2.7492, -1.7965, 0.899, -2.2295, -1.6842, -0.4729, -2.3712, -2.5502, 1.5321, -0.8246, -2.3999, -0.6295, -1.5513, -2.3834, 0.7782, -1.5631, -2.3999, -0.6295, -1.5513, -2.907, 0.041, -0.3088, -2.5883, -1.4508, -0.7517, -2.3999, -0.6295, -1.5513, -2.5502, 1.5321, -0.8246, -2.907, 0.041, -0.3088, 1.1377, -0.8428, -2.7439, 1.4453, 1.3219, -1.8647, 0.9877, 0.4313, -2.4389, 1.4453, 1.3219, -1.8647, 1.9584, -0.8816, -2.0923, 2.1515, 0.9506, -1.6595, 1.4453, 1.3219, -1.8647, 1.1377, -0.8428, -2.7439, 1.9584, -0.8816, -2.0923, -0.0823, 1.3076, -2.4751, 0.7304, -0.9533, -2.8604, 0.635, 0.4247, -2.5749, 0.7304, -0.9533, -2.8604, -0.7692, -0.6389, -2.6654, 0.0974, -1.7758, -2.6186, -0.7692, -0.6389, -2.6654, -0.0823, 1.3076, -2.4751, -0.7261, 0.8081, -2.8158, 0.7304, -0.9533, -2.8604, -0.0823, 1.3076, -2.4751, -0.7692, -0.6389, -2.6654, 1.2634, 3.7734, 1.9919, 2.1901, 2.3652, 1.3543, 1.6219, 2.267, 2.1861, 2.1901, 2.3652, 1.3543, 1.6148, 4.6612, 1.1746, 2.2544, 3.8319, 0.7934, 2.1901, 2.3652, 1.3543, 1.2634, 3.7734, 1.9919, 1.6148, 4.6612, 1.1746, -1.3001, 2.2705, 2.1959, -0.878, 3.135, 1.7811, -0.4271, 2.5556, 2.4897, -1.9764, 3.1849, 0.2685, -2.575, 1.9843, 0.2578, -2.286, 2.7057, -0.3882, -0.2095, 1.8203, -2.3278, -0.9393, 2.872, -1.8539, -0.8202, 1.5078, -2.6285, -0.2095, 1.8203, -2.3278, -0.0988, 3.0024, -1.9036, -0.9393, 2.872, -1.8539, 1.4442, 1.9, -1.6223, 2.0312, 2.7158, -1.0135, 1.1827, 2.8613, -1.4341, -0.0735, 4.938, 0.8308, 0.5261, 4.2194, 1.9538, -0.2982, 4.0929, 1.4648, -0.0735, 4.938, 0.8308, 0.8594, 5.2909, 1.0912, 0.5261, 4.2194, 1.9538, 1.3249, 5.3073, 0.5102, 1.7744, 4.3299, -0.4393, 2.1174, 4.2399, 0.396, 1.3249, 5.3073, 0.5102, 0.9445, 5.5277, -0.3666, 1.7744, 4.3299, -0.4393, -1.7496, 3.0465, -0.6307, -0.791, 4.3118, -0.0929, -1.3695, 3.5538, -0.0189, -1.7496, 3.0465, -0.6307, -1.2485, 3.6723, -1.0264, -0.791, 4.3118, -0.0929, 0.9077, 3.2903, -1.4616, -0.1392, 4.5027, -1.2571, 0.1823, 3.4347, -1.7089, -0.1392, 4.5027, -1.2571, 1.3226, 4.3217, -0.9655, 0.6884, 5.4332, -0.7011, -0.1392, 4.5027, -1.2571, 0.9077, 3.2903, -1.4616, 1.3226, 4.3217, -0.9655, -0.8729, -4.7982, 1.121, 0.169, -4.9369, 0.2441, -0.4293, -5.0774, 0.0003, -0.8729, -4.7982, 1.121, -0.0922, -4.5871, 1.3087, 0.169, -4.9369, 0.2441, 2.3387, -2.1373, 0.6494, 1.8065, -2.8661, 1.2715, 2.1498, -1.9824, 1.4097, 2.3387, -2.1373, 0.6494, 1.9869, -3.1191, 0.2426, 1.8065, -2.8661, 1.2715, 0.9342, -3.6502, -1.2505, 1.87, -2.8735, -1.1419, 1.4243, -3.0391, -1.5379, 0.9342, -3.6502, -1.2505, 1.5863, -3.2514, -0.7541, 1.87, -2.8735, -1.1419, -2.3406, -2.1709, 1.3672, -2.179, -3.4152, 0.4259, -2.6648, -1.8501, 0.307, -2.3406, -2.1709, 1.3672, -1.8849, -3.8097, 1.1593, -2.179, -3.4152, 0.4259, 0.1398, -4.5397, -0.9338, -0.4293, -5.0774, 0.0003, 0.0671, -5.0216, -0.4515, 0.1398, -4.5397, -0.9338, -0.5108, -3.8408, -1.1476, -0.4293, -5.0774, 0.0003, -1.6361, -1.5711, -1.5378, -0.2695, -2.508, -2.0432, -1.0077, -1.3716, -2.1657, -1.6361, -1.5711, -1.5378, -1.1054, -2.8992, -1.0824, -0.2695, -2.508, -2.0432, 1.5426, -2.0265, -2.2438, 1.4243, -3.0391, -1.5379, 2.1195, -1.771, -1.5705, 1.5426, -2.0265, -2.2438, 0.9178, -3.3811, -1.9588, 1.4243, -3.0391, -1.5379, 2.6003, 1.0473, -1.0133, 2.5473, 1.387, -0.0726, 2.4031, 2.6779, -0.4122, 2.6003, 1.0473, -1.0133, 2.6196, 0.7185, -0.2944, 2.5473, 1.387, -0.0726, -0.7814, -0.8418, 2.7775, -0.2939, -2.4569, 2.4482, -1.2288, -2.368, 2.4204, -0.7814, -0.8418, 2.7775, 0.256, -0.9397, 2.7009, -0.2939, -2.4569, 2.4482, -1.6176, 1.4128, 2.2491, -2.1461, 1.5782, 1.2673, -1.7058, 2.0492, 1.8707, -1.6176, 1.4128, 2.2491, -2.0758, -0.0137, 1.8949, -2.1461, 1.5782, 1.2673, -2.5502, 1.5321, -0.8246, -1.7216, 2.2099, -1.5001, -1.9974, 2.3952, -0.8364, -2.5502, 1.5321, -0.8246, -2.3834, 0.7782, -1.5631, -1.7216, 2.2099, -1.5001, -0.0823, 1.3076, -2.4751, 0.7444, 0.8429, -2.2856, 0.0456, 1.662, -2.2329, -0.0823, 1.3076, -2.4751, 0.635, 0.4247, -2.5749, 0.7444, 0.8429, -2.2856, 1.2634, 3.7734, 1.9919, 1.1136, 1.849, 2.4998, 0.5038, 3.0339, 2.4087, 1.2634, 3.7734, 1.9919, 1.6219, 2.267, 2.1861, 1.1136, 1.849, 2.4998, -0.878, 3.135, 1.7811, -1.7058, 2.0492, 1.8707, -1.5979, 2.7577, 1.2075, -0.878, 3.135, 1.7811, -1.3001, 2.2705, 2.1959, -1.7058, 2.0492, 1.8707, -0.0988, 3.0024, -1.9036, 0.0456, 1.662, -2.2329, 0.1709, 2.9305, -1.8154, -0.0988, 3.0024, -1.9036, -0.2095, 1.8203, -2.3278, 0.0456, 1.662, -2.2329, 0.9445, 5.5277, -0.3666, 0.6378, 5.6752, 0.4017, 0.5746, 5.6649, -0.3989, 0.9445, 5.5277, -0.3666, 1.3249, 5.3073, 0.5102, 0.6378, 5.6752, 0.4017, -1.2485, 3.6723, -1.0264, -1.9974, 2.3952, -0.8364, -1.7216, 2.2099, -1.5001, -1.2485, 3.6723, -1.0264, -1.7496, 3.0465, -0.6307, -1.9974, 2.3952, -0.8364, 0.9077, 3.2903, -1.4616, 0.1709, 2.9305, -1.8154, 0.9177, 2.8679, -1.5387, 0.9077, 3.2903, -1.4616, 0.1823, 3.4347, -1.7089, 0.1709, 2.9305, -1.8154, -1.2288, -2.368, 2.4204, -0.2829, -3.5923, 2.1108, -1.1122, -3.8993, 1.9946, -1.2288, -2.368, 2.4204, -0.2939, -2.4569, 2.4482, -0.2829, -3.5923, 2.1108, 1.6056, -1.9424, 1.9813, 0.9704, -3.0065, 1.4497, 0.5442, -2.0915, 2.2664, 1.6056, -1.9424, 1.9813, 1.8065, -2.8661, 1.2715, 0.9704, -3.0065, 1.4497, 2.1195, -1.771, -1.5705, 2.1726, -2.8941, -0.5813, 2.5358, -1.9021, -0.8254, 2.1195, -1.771, -1.5705, 1.87, -2.8735, -1.1419, 2.1726, -2.8941, -0.5813, 0.0671, -5.0216, -0.4515, 0.6021, -4.1961, -0.0392, 0.4756, -4.5162, -0.6549, 0.0671, -5.0216, -0.4515, 0.169, -4.9369, 0.2441, 0.6021, -4.1961, -0.0392, -2.3688, -2.5627, -0.2622, -1.1208, -4.5065, 0.1953, -1.809, -3.263, -0.2744, -2.3688, -2.5627, -0.2622, -2.179, -3.4152, 0.4259, -1.1208, -4.5065, 0.1953, 2.3678, 2.1513, 0.6399, 2.4617, 0.7259, 0.0288, 2.1038, 1.0245, 0.9114, 2.3678, 2.1513, 0.6399, 2.5473, 1.387, -0.0726, 2.4617, 0.7259, 0.0288, 0.5038, 3.0339, 2.4087, 0.7315, 0.8274, 2.7196, -0.2195, 1.1266, 2.8293, 0.5038, 3.0339, 2.4087, 1.1136, 1.849, 2.4998, 0.7315, 0.8274, 2.7196, 2.1498, -1.9824, 1.4097, 1.2883, -0.8696, 2.2885, 1.9393, -0.8033, 1.6608, 2.1498, -1.9824, 1.4097, 1.6056, -1.9424, 1.9813, 1.2883, -0.8696, 2.2885, -2.6648, -1.8501, 0.307, -2.5883, -1.4508, -0.7517, -2.907, 0.041, -0.3088, -2.6648, -1.8501, 0.307, -2.3688, -2.5627, -0.2622, -2.5883, -1.4508, -0.7517, 1.2471, 1.631, -1.7857, 0.9877, 0.4313, -2.4389, 1.4453, 1.3219, -1.8647, 1.2471, 1.631, -1.7857, 0.7444, 0.8429, -2.2856, 0.9877, 0.4313, -2.4389, -1.0077, -1.3716, -2.1657, 0.0974, -1.7758, -2.6186, -0.7692, -0.6389, -2.6654, -1.0077, -1.3716, -2.1657, -0.2695, -2.508, -2.0432, 0.0974, -1.7758, -2.6186, 2.3685, 3.5019, 0.414, 2.1901, 2.3652, 1.3543, 2.2544, 3.8319, 0.7934, 2.3685, 3.5019, 0.414, 2.3678, 2.1513, 0.6399, 2.1901, 2.3652, 1.3543, -1.5979, 2.7577, 1.2075, -2.575, 1.9843, 0.2578, -1.9764, 3.1849, 0.2685, -1.5979, 2.7577, 1.2075, -2.1461, 1.5782, 1.2673, -2.575, 1.9843, 0.2578, 0.9177, 2.8679, -1.5387, 1.4442, 1.9, -1.6223, 1.1827, 2.8613, -1.4341, 0.9177, 2.8679, -1.5387, 1.2471, 1.631, -1.7857, 1.4442, 1.9, -1.6223, -0.2232, 5.1794, 0.1938, 0.8594, 5.2909, 1.0912, -0.0735, 4.938, 0.8308, -0.2232, 5.1794, 0.1938, 0.6378, 5.6752, 0.4017, 0.8594, 5.2909, 1.0912, 2.4031, 2.6779, -0.4122, 2.1174, 4.2399, 0.396, 1.7744, 4.3299, -0.4393, 2.4031, 2.6779, -0.4122, 2.3685, 3.5019, 0.414, 2.1174, 4.2399, 0.396, -0.2125, 5.045, -0.6732, -0.791, 4.3118, -0.0929, -1.2485, 3.6723, -1.0264, -0.2125, 5.045, -0.6732, -0.2232, 5.1794, 0.1938, -0.791, 4.3118, -0.0929, 0.5746, 5.6649, -0.3989, -0.1392, 4.5027, -1.2571, 0.6884, 5.4332, -0.7011, 0.5746, 5.6649, -0.3989, -0.2125, 5.045, -0.6732, -0.1392, 4.5027, -1.2571, 1.7744, 4.3299, -0.4393, 0.6884, 5.4332, -0.7011, 1.3226, 4.3217, -0.9655, 1.7744, 4.3299, -0.4393, 0.9445, 5.5277, -0.3666, 0.6884, 5.4332, -0.7011, 1.3226, 4.3217, -0.9655, 1.1827, 2.8613, -1.4341, 2.0312, 2.7158, -1.0135, 1.3226, 4.3217, -0.9655, 0.9077, 3.2903, -1.4616, 1.1827, 2.8613, -1.4341, 0.1823, 3.4347, -1.7089, -0.9393, 2.872, -1.8539, -0.0988, 3.0024, -1.9036, 0.1823, 3.4347, -1.7089, -0.1392, 4.5027, -1.2571, -0.9393, 2.872, -1.8539, -1.3695, 3.5538, -0.0189, -0.5404, 4.4098, 0.4328, -1.0116, 3.5059, 0.8103, -1.3695, 3.5538, -0.0189, -0.791, 4.3118, -0.0929, -0.5404, 4.4098, 0.4328, -1.7496, 3.0465, -0.6307, -1.9764, 3.1849, 0.2685, -2.286, 2.7057, -0.3882, -1.7496, 3.0465, -0.6307, -1.3695, 3.5538, -0.0189, -1.9764, 3.1849, 0.2685, -1.0116, 3.5059, 0.8103, -0.0735, 4.938, 0.8308, -0.2982, 4.0929, 1.4648, -1.0116, 3.5059, 0.8103, -0.5404, 4.4098, 0.4328, -0.0735, 4.938, 0.8308, -0.2982, 4.0929, 1.4648, -0.4271, 2.5556, 2.4897, -0.878, 3.135, 1.7811, -0.2982, 4.0929, 1.4648, 0.5261, 4.2194, 1.9538, -0.4271, 2.5556, 2.4897, 0.5261, 4.2194, 1.9538, 1.6148, 4.6612, 1.1746, 1.2634, 3.7734, 1.9919, 0.5261, 4.2194, 1.9538, 0.8594, 5.2909, 1.0912, 1.6148, 4.6612, 1.1746, 1.3249, 5.3073, 0.5102, 2.2544, 3.8319, 0.7934, 1.6148, 4.6612, 1.1746, 1.3249, 5.3073, 0.5102, 2.1174, 4.2399, 0.396, 2.2544, 3.8319, 0.7934, 2.5987, -0.8743, -1.1872, 2.1515, 0.9506, -1.6595, 1.9584, -0.8816, -2.0923, 2.5987, -0.8743, -1.1872, 2.6003, 1.0473, -1.0133, 2.1515, 0.9506, -1.6595, 2.0312, 2.7158, -1.0135, 1.4453, 1.3219, -1.8647, 2.1515, 0.9506, -1.6595, 2.0312, 2.7158, -1.0135, 1.4442, 1.9, -1.6223, 1.4453, 1.3219, -1.8647, -0.7261, 0.8081, -2.8158, -0.2095, 1.8203, -2.3278, -0.8202, 1.5078, -2.6285, -0.7261, 0.8081, -2.8158, -0.0823, 1.3076, -2.4751, -0.2095, 1.8203, -2.3278, -0.7692, -0.6389, -2.6654, -1.246, 0.719, -2.7492, -1.6842, -0.4729, -2.3712, -0.7692, -0.6389, -2.6654, -0.7261, 0.8081, -2.8158, -1.246, 0.719, -2.7492, -0.8202, 1.5078, -2.6285, -1.7965, 0.899, -2.2295, -1.246, 0.719, -2.7492, -0.8202, 1.5078, -2.6285, -0.9393, 2.872, -1.8539, -1.7965, 0.899, -2.2295, -2.907, 0.041, -0.3088, -2.286, 2.7057, -0.3882, -2.575, 1.9843, 0.2578, -2.907, 0.041, -0.3088, -2.5502, 1.5321, -0.8246, -2.286, 2.7057, -0.3882, -1.0159, 0.9756, 2.6776, -1.3001, 2.2705, 2.1959, -0.4271, 2.5556, 2.4897, -1.0159, 0.9756, 2.6776, -1.6176, 1.4128, 2.2491, -1.3001, 2.2705, 2.1959, -1.4963, -0.9921, 2.5267, -0.2195, 1.1266, 2.8293, -0.7814, -0.8418, 2.7775, -1.4963, -0.9921, 2.5267, -1.0159, 0.9756, 2.6776, -0.2195, 1.1266, 2.8293, 1.9293, 0.908, 1.6849, 1.6219, 2.267, 2.1861, 2.1901, 2.3652, 1.3543, 1.9293, 0.908, 1.6849, 1.409, 0.9631, 2.386, 1.6219, 2.267, 2.1861, 1.9393, -0.8033, 1.6608, 2.1038, 1.0245, 0.9114, 2.1074, -0.9551, 0.9434, 1.9393, -0.8033, 1.6608, 1.9293, 0.908, 1.6849, 2.1038, 1.0245, 0.9114, 1.1377, -0.8428, -2.7439, 0.635, 0.4247, -2.5749, 0.7304, -0.9533, -2.8604, 1.1377, -0.8428, -2.7439, 0.9877, 0.4313, -2.4389, 0.635, 0.4247, -2.5749, 1.9584, -0.8816, -2.0923, 0.9457, -1.6804, -2.7624, 1.5426, -2.0265, -2.2438, 1.9584, -0.8816, -2.0923, 1.1377, -0.8428, -2.7439, 0.9457, -1.6804, -2.7624, 0.7304, -0.9533, -2.8604, 0.3194, -2.8417, -2.3407, 0.9457, -1.6804, -2.7624, 0.7304, -0.9533, -2.8604, 0.0974, -1.7758, -2.6186, 0.3194, -2.8417, -2.3407, -1.6842, -0.4729, -2.3712, -2.3834, 0.7782, -1.5631, -2.3999, -0.6295, -1.5513, -1.6842, -0.4729, -2.3712, -1.7965, 0.899, -2.2295, -2.3834, 0.7782, -1.5631, -2.3999, -0.6295, -1.5513, -1.9059, -2.3487, -0.7483, -1.6361, -1.5711, -1.5378, -2.3999, -0.6295, -1.5513, -2.5883, -1.4508, -0.7517, -1.9059, -2.3487, -0.7483, -2.0758, -0.0137, 1.8949, -1.7887, -3.0231, 1.9828, -2.3406, -2.1709, 1.3672, -2.0758, -0.0137, 1.8949, -1.4963, -0.9921, 2.5267, -1.7887, -3.0231, 1.9828, 0.256, -0.9397, 2.7009, 1.409, 0.9631, 2.386, 1.2883, -0.8696, 2.2885, 0.256, -0.9397, 2.7009, 0.7315, 0.8274, 2.7196, 1.409, 0.9631, 2.386, 2.4385, -0.7713, 0.0524, 2.6196, 0.7185, -0.2944, 2.634, -0.7691, -0.3318, 2.4385, -0.7713, 0.0524, 2.4617, 0.7259, 0.0288, 2.6196, 0.7185, -0.2944, 2.1074, -0.9551, 0.9434, 2.5652, -1.538, -0.1083, 2.3387, -2.1373, 0.6494, 2.1074, -0.9551, 0.9434, 2.4385, -0.7713, 0.0524, 2.5652, -1.538, -0.1083, 2.634, -0.7691, -0.3318, 2.5358, -1.9021, -0.8254, 2.5652, -1.538, -0.1083, 2.634, -0.7691, -0.3318, 2.5987, -0.8743, -1.1872, 2.5358, -1.9021, -0.8254, 0.4756, -4.5162, -0.6549, 0.3423, -3.927, -1.4233, 0.1398, -4.5397, -0.9338, 0.4756, -4.5162, -0.6549, 0.9342, -3.6502, -1.2505, 0.3423, -3.927, -1.4233, 0.9178, -3.3811, -1.9588, -0.5108, -3.8408, -1.1476, 0.3423, -3.927, -1.4233, 0.9178, -3.3811, -1.9588, 0.3194, -2.8417, -2.3407, -0.5108, -3.8408, -1.1476, -1.1054, -2.8992, -1.0824, -1.809, -3.263, -0.2744, -1.1208, -4.5065, 0.1953, -1.1054, -2.8992, -1.0824, -1.9059, -2.3487, -0.7483, -1.809, -3.263, -0.2744, -1.8849, -3.8097, 1.1593, -1.1122, -3.8993, 1.9946, -0.8729, -4.7982, 1.121, -1.8849, -3.8097, 1.1593, -1.7887, -3.0231, 1.9828, -1.1122, -3.8993, 1.9946, 1.9869, -3.1191, 0.2426, 1.5863, -3.2514, -0.7541, 1.267, -3.4909, -0.189, 1.9869, -3.1191, 0.2426, 2.1726, -2.8941, -0.5813, 1.5863, -3.2514, -0.7541, 1.267, -3.4909, -0.189, 0.4584, -3.889, 0.8266, 1.2817, -3.3103, 0.5621, 1.267, -3.4909, -0.189, 0.6021, -4.1961, -0.0392, 0.4584, -3.889, 0.8266, -0.0922, -4.5871, 1.3087, 0.5442, -2.0915, 2.2664, 0.4197, -3.4404, 1.5487, -0.0922, -4.5871, 1.3087, -0.2829, -3.5923, 2.1108, 0.5442, -2.0915, 2.2664, 0.4197, -3.4404, 1.5487, 1.2817, -3.3103, 0.5621, 0.4584, -3.889, 0.8266, 0.4197, -3.4404, 1.5487, 0.9704, -3.0065, 1.4497, 1.2817, -3.3103, 0.5621, -0.4293, -5.0774, 0.0003, -1.1208, -4.5065, 0.1953, -0.8729, -4.7982, 1.121)

[node name="RockB" instance=ExtResource("1_2nhli")]
collision_mask = 3

[node name="Rock2" parent="." index="0"]
surface_material_override/0 = ExtResource("2_dbm2k")

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="1"]
shape = SubResource("ConcavePolygonShape3D_wfrmp")
