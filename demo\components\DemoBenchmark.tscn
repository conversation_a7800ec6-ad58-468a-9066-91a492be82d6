[gd_scene load_steps=11 format=3 uid="uid://dyt8c2xqmddo2"]

[ext_resource type="Script" uid="uid://chstoagn42gbr" path="res://demo/src/DemoScene.gd" id="1_2gjn4"]
[ext_resource type="PackedScene" uid="uid://d2jihfohphuue" path="res://demo/components/UI.tscn" id="2_3obxr"]
[ext_resource type="PackedScene" uid="uid://bb2lp50sjndus" path="res://demo/components/Environment.tscn" id="3_b7ioy"]
[ext_resource type="PackedScene" uid="uid://dwnhqfjq7v1pq" path="res://demo/components/Borders.tscn" id="4_nn6wp"]
[ext_resource type="PackedScene" uid="uid://domhm87hbhbg1" path="res://demo/components/Player.tscn" id="5_bwggt"]
[ext_resource type="Terrain3DAssets" uid="uid://dal3jhw6241qg" path="res://demo/data/assets.tres" id="6_3r2au"]

[sub_resource type="Gradient" id="Gradient_f1plm"]
offsets = PackedFloat32Array(0.2, 1)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_bfcw0"]
noise_type = 2
frequency = 0.03
cellular_jitter = 3.0
cellular_return_type = 0
domain_warp_enabled = true
domain_warp_type = 1
domain_warp_amplitude = 50.0
domain_warp_fractal_type = 2
domain_warp_fractal_lacunarity = 1.5
domain_warp_fractal_gain = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_quxx0"]
seamless = true
color_ramp = SubResource("Gradient_f1plm")
noise = SubResource("FastNoiseLite_bfcw0")

[sub_resource type="Terrain3DMaterial" id="Terrain3DMaterial_klrp5"]
_shader_parameters = {
"auto_base_texture": 0,
"auto_height_reduction": 0.1,
"auto_overlay_texture": 1,
"auto_slope": 1.0,
"bias_distance": 512.0,
"blend_sharpness": 0.87,
"depth_blur": 0.0,
"dual_scale_far": 170.0,
"dual_scale_near": 100.0,
"dual_scale_reduction": 0.3,
"dual_scale_texture": 0,
"enable_macro_variation": true,
"enable_projection": true,
"height_blending": true,
"macro_variation1": Color(1, 1, 1, 1),
"macro_variation2": Color(1, 1, 1, 1),
"macro_variation_slope": 0.333,
"mipmap_bias": 1.0,
"noise1_angle": 0.0,
"noise1_offset": Vector2(0.5, 0.5),
"noise1_scale": 0.04,
"noise2_scale": 0.076,
"noise3_scale": 0.225,
"noise_texture": SubResource("NoiseTexture2D_quxx0"),
"projection_angular_division": 1.436,
"projection_threshold": 0.8,
"tri_scale_reduction": 0.3,
&"world_space_normal_blend": true
}
auto_shader = true
dual_scaling = true

[node name="Demo" type="Node"]
script = ExtResource("1_2gjn4")

[node name="UI" parent="." instance=ExtResource("2_3obxr")]

[node name="World" parent="." instance=ExtResource("3_b7ioy")]

[node name="Borders" parent="." instance=ExtResource("4_nn6wp")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 512, 0, 512)

[node name="Player" parent="." instance=ExtResource("5_bwggt")]
transform = Transform3D(0.134125, 0, -0.990965, 0, 1, 0, 0.990965, 0, 0.134125, 216.455, 103.968, -1835.34)

[node name="Terrain3D" type="Terrain3D" parent="."]
data_directory = "res://demo/data"
material = SubResource("Terrain3DMaterial_klrp5")
assets = ExtResource("6_3r2au")
collision_mask = 3
top_level = true
metadata/_edit_lock_ = true
